<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hover Animation Test</title>
    <style>
        /* Test hover animations */
        .test-card {
            width: 300px;
            height: 200px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            margin: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
        }

        /* Media query test for hover capability */
        @media (hover: hover) and (pointer: fine) {
            .test-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                border-color: #3b82f6;
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            }
            
            .test-card::before {
                content: '🖱️ Hover Capable Device';
                position: absolute;
                top: -30px;
                left: 0;
                font-size: 12px;
                color: #10b981;
                background: #ecfdf5;
                padding: 4px 8px;
                border-radius: 4px;
            }
        }

        /* Touch device test */
        @media (hover: none) and (pointer: coarse) {
            .test-card::before {
                content: '📱 Touch Device';
                position: absolute;
                top: -30px;
                left: 0;
                font-size: 12px;
                color: #f59e0b;
                background: #fffbeb;
                padding: 4px 8px;
                border-radius: 4px;
            }
            
            .test-card:hover {
                transform: none !important;
                box-shadow: none !important;
                background: inherit !important;
            }
        }

        /* Fallback hover that should work on all devices */
        .fallback-card {
            width: 300px;
            height: 200px;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .fallback-card:hover {
            transform: translateY(-4px) scale(1.02) !important;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
            border-color: #d97706 !important;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 20px;
            background: #f9fafb;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            color: #1f2937;
            margin-bottom: 20px;
        }

        .description {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hover Animation Test</h1>
        
        <div class="description">
            <h2>Test Instructions:</h2>
            <ul>
                <li><strong>Desktop with Mouse:</strong> You should see "🖱️ Hover Capable Device" and cards should lift up on hover</li>
                <li><strong>Touch Device:</strong> You should see "📱 Touch Device" and hover effects should be disabled</li>
                <li><strong>Fallback Test:</strong> The yellow card should always lift up on hover regardless of device</li>
            </ul>
        </div>

        <div style="display: flex; flex-wrap: wrap; gap: 20px;">
            <div class="test-card">
                <h3>Media Query Test Card</h3>
                <p>This card uses @media (hover: hover) and (pointer: fine) to detect hover capability.</p>
                <p>Hover behavior should adapt based on your device type.</p>
            </div>

            <div class="fallback-card">
                <h3>Fallback Test Card</h3>
                <p>This card should always show hover effects regardless of device type.</p>
                <p>If this doesn't work, there's a fundamental CSS issue.</p>
            </div>
        </div>

        <div class="description" style="margin-top: 30px;">
            <h3>Expected Results:</h3>
            <p><strong>Desktop:</strong> Both cards should lift up on hover, with the first card showing a blue theme and the second showing enhanced yellow.</p>
            <p><strong>Mobile/Tablet:</strong> Only the yellow fallback card should show hover effects (if any), the first card should remain static.</p>
        </div>
    </div>
</body>
</html>
