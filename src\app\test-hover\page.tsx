'use client';

import React from 'react';
import { TouchableCard } from '@/components/ui/TouchableCard';
import { TouchableButton } from '@/components/ui/TouchableButton';
import { TouchableCalculatorButton } from '@/components/calculators/TouchableCalculatorButton';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Heart, Share2, Calculator } from 'lucide-react';

export default function TestHoverPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="py-8 px-4">
        <div className="max-w-6xl mx-auto space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-4">Touch & Hover Test Page</h1>
            <p className="text-muted-foreground mb-6">
              Test both touch interactions (mobile/tablet) and hover animations (desktop)
            </p>
            <Badge variant="outline" className="mb-4">
              Desktop: Hover animations | Mobile/Tablet: Touch interactions
            </Badge>
          </div>

          {/* Blog Card Test */}
          <Card>
            <CardHeader>
              <CardTitle>Blog Card Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <TouchableCard
                  onTap={() => console.log('Blog card tapped')}
                  onDoubleTap={() => console.log('Blog card double-tapped')}
                  onLongPress={() => console.log('Blog card long-pressed')}
                  onSwipeLeft={() => console.log('Blog card swiped left')}
                  onSwipeRight={() => console.log('Blog card swiped right')}
                  cardType="blog"
                  enableHoverAnimations={true}
                  className="h-64"
                >
                  <div className="h-full flex flex-col">
                    <div className="blog-image h-32 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mb-4"></div>
                    <div className="card-content flex-1 space-y-2">
                      <Badge className="category-badge">Technology</Badge>
                      <h3 className="card-title font-semibold">Sample Blog Post</h3>
                      <p className="text-sm text-muted-foreground">This is a sample blog post description...</p>
                      <div className="flex items-center justify-between mt-auto">
                        <span className="text-sm">Read more</span>
                        <ArrowRight className="hover-arrow w-4 h-4" />
                      </div>
                    </div>
                  </div>
                </TouchableCard>
              </div>
            </CardContent>
          </Card>

          {/* Tool Card Test */}
          <Card>
            <CardHeader>
              <CardTitle>Tool Card Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <TouchableCard
                  onTap={() => console.log('Tool card tapped')}
                  onLongPress={() => console.log('Tool card long-pressed')}
                  onSwipeLeft={() => console.log('Tool card swiped left')}
                  onSwipeRight={() => console.log('Tool card swiped right')}
                  cardType="tool"
                  enableHoverAnimations={true}
                  className="h-48"
                >
                  <div className="h-full flex flex-col">
                    <div className="flex items-start justify-between mb-4">
                      <div className="tool-icon text-3xl">🔧</div>
                      <Badge variant="secondary">PDF</Badge>
                    </div>
                    <h3 className="card-title font-semibold mb-2">PDF Converter</h3>
                    <p className="text-sm text-muted-foreground mb-4">Convert your files to PDF format</p>
                    <div className="flex items-center justify-between mt-auto">
                      <span className="text-sm">PDF → DOC</span>
                      <ArrowRight className="hover-arrow w-4 h-4" />
                    </div>
                  </div>
                </TouchableCard>
              </div>
            </CardContent>
          </Card>

          {/* Calculator Card Test */}
          <Card>
            <CardHeader>
              <CardTitle>Calculator Card Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <TouchableCard
                  onTap={() => console.log('Calculator card tapped')}
                  onLongPress={() => console.log('Calculator card long-pressed')}
                  onSwipeLeft={() => console.log('Calculator card swiped left')}
                  onSwipeRight={() => console.log('Calculator card swiped right')}
                  cardType="calculator"
                  enableHoverAnimations={true}
                  className="h-48"
                >
                  <div className="h-full flex flex-col">
                    <div className="flex items-start justify-between mb-4">
                      <div className="hover-icon text-3xl p-2 bg-blue-100 rounded-lg">
                        <Calculator className="w-8 h-8 text-blue-600" />
                      </div>
                      <Badge variant="secondary">Finance</Badge>
                    </div>
                    <h3 className="card-title font-semibold mb-2">Mortgage Calculator</h3>
                    <p className="text-sm text-muted-foreground mb-4">Calculate your monthly payments</p>
                    <div className="flex items-center justify-end mt-auto">
                      <ArrowRight className="hover-arrow w-4 h-4" />
                    </div>
                  </div>
                </TouchableCard>
              </div>
            </CardContent>
          </Card>

          {/* Button Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Button Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <TouchableButton
                  onTap={() => console.log('Primary button tapped')}
                  variant="primary"
                  size="lg"
                >
                  Primary Button
                </TouchableButton>
                
                <TouchableButton
                  onTap={() => console.log('Secondary button tapped')}
                  variant="secondary"
                  size="lg"
                >
                  Secondary Button
                </TouchableButton>
                
                <TouchableButton
                  onTap={() => console.log('Outline button tapped')}
                  variant="outline"
                  size="lg"
                >
                  Outline Button
                </TouchableButton>
                
                <TouchableButton
                  onTap={() => console.log('Ghost button tapped')}
                  variant="ghost"
                  size="lg"
                >
                  Ghost Button
                </TouchableButton>
              </div>
            </CardContent>
          </Card>

          {/* Calculator Button Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Calculator Button Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4 max-w-md mx-auto">
                <TouchableCalculatorButton
                  onClick={() => console.log('Number 7 pressed')}
                  variant="number"
                  size="lg"
                >
                  7
                </TouchableCalculatorButton>
                
                <TouchableCalculatorButton
                  onClick={() => console.log('Number 8 pressed')}
                  variant="number"
                  size="lg"
                >
                  8
                </TouchableCalculatorButton>
                
                <TouchableCalculatorButton
                  onClick={() => console.log('Number 9 pressed')}
                  variant="number"
                  size="lg"
                >
                  9
                </TouchableCalculatorButton>
                
                <TouchableCalculatorButton
                  onClick={() => console.log('Operator + pressed')}
                  variant="operator"
                  size="lg"
                >
                  +
                </TouchableCalculatorButton>
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Testing Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Desktop (Mouse) Testing:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Hover over cards to see smooth animations</li>
                    <li>Cards should lift up and show shadow effects</li>
                    <li>Icons and arrows should animate on hover</li>
                    <li>Buttons should have subtle hover effects</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Mobile/Tablet (Touch) Testing:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Tap cards for immediate feedback</li>
                    <li>Double-tap blog cards to like</li>
                    <li>Long press (750ms) for context menus</li>
                    <li>Swipe left/right on cards for actions</li>
                    <li>Check console for touch event logs</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
}
