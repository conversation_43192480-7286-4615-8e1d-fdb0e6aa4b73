'use client';

import React from 'react';
import { TouchableCard } from '@/components/ui/TouchableCard';
import { TouchableButton } from '@/components/ui/TouchableButton';
import { TouchableCalculatorButton } from '@/components/calculators/TouchableCalculatorButton';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Heart, Share2, Calculator } from 'lucide-react';

export default function TestHoverPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="py-8 px-4">
        <div className="max-w-6xl mx-auto space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-4">Touch & Hover Test Page</h1>
            <p className="text-muted-foreground mb-6">
              Test both touch interactions (mobile/tablet) and hover animations (desktop)
            </p>
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              <Badge variant="outline">
                Desktop: Hover animations
              </Badge>
              <Badge variant="outline">
                Mobile/Tablet: Touch interactions
              </Badge>
              <Badge variant="secondary" className="text-xs">
                CSS Media Query: @media (hover: hover) and (pointer: fine)
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              <p>🖱️ Desktop: Hover over cards to see lift animations</p>
              <p>📱 Mobile: Tap, swipe, and long press for interactions</p>
            </div>
          </div>

          {/* CSS Debug Test */}
          <Card>
            <CardHeader>
              <CardTitle className="debug-css-loaded">CSS Loading Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="test-hover-card p-6 rounded-lg">
                  <h3 className="font-semibold mb-2">Fallback Hover Test</h3>
                  <p className="text-sm text-muted-foreground">
                    This card should lift up on hover regardless of device type.
                    If this works, CSS is loading correctly.
                  </p>
                </div>
                <div className="p-6 rounded-lg border-2 border-green-200 bg-green-50">
                  <h3 className="font-semibold mb-2 text-green-800">CSS Status</h3>
                  <p className="text-sm text-green-700">
                    Look for "✅ CSS Loaded" indicator above the title.
                    If visible, touch.css is loading properly.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Blog Card Test */}
          <Card>
            <CardHeader>
              <CardTitle>Blog Card Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <TouchableCard
                  onTap={() => console.log('Blog card tapped')}
                  onDoubleTap={() => console.log('Blog card double-tapped')}
                  onLongPress={() => console.log('Blog card long-pressed')}
                  onSwipeLeft={() => console.log('Blog card swiped left')}
                  onSwipeRight={() => console.log('Blog card swiped right')}
                  cardType="blog"
                  enableHoverAnimations={true}
                  className="h-64 border-2 border-dashed border-blue-200"
                  style={{
                    background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                  }}
                >
                  <div className="h-full flex flex-col">
                    <div className="blog-image h-32 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mb-4"></div>
                    <div className="card-content flex-1 space-y-2">
                      <Badge className="category-badge">Technology</Badge>
                      <h3 className="card-title font-semibold">Sample Blog Post</h3>
                      <p className="text-sm text-muted-foreground">This is a sample blog post description...</p>
                      <div className="flex items-center justify-between mt-auto">
                        <span className="text-sm">Read more</span>
                        <ArrowRight className="hover-arrow w-4 h-4" />
                      </div>
                    </div>
                  </div>
                </TouchableCard>
              </div>
            </CardContent>
          </Card>

          {/* Tool Card Test */}
          <Card>
            <CardHeader>
              <CardTitle>Tool Card Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <TouchableCard
                  onTap={() => console.log('Tool card tapped')}
                  onLongPress={() => console.log('Tool card long-pressed')}
                  onSwipeLeft={() => console.log('Tool card swiped left')}
                  onSwipeRight={() => console.log('Tool card swiped right')}
                  cardType="tool"
                  enableHoverAnimations={true}
                  className="h-48"
                >
                  <div className="h-full flex flex-col">
                    <div className="flex items-start justify-between mb-4">
                      <div className="tool-icon text-3xl">🔧</div>
                      <Badge variant="secondary">PDF</Badge>
                    </div>
                    <h3 className="card-title font-semibold mb-2">PDF Converter</h3>
                    <p className="text-sm text-muted-foreground mb-4">Convert your files to PDF format</p>
                    <div className="flex items-center justify-between mt-auto">
                      <span className="text-sm">PDF → DOC</span>
                      <ArrowRight className="hover-arrow w-4 h-4" />
                    </div>
                  </div>
                </TouchableCard>
              </div>
            </CardContent>
          </Card>

          {/* Calculator Card Test */}
          <Card>
            <CardHeader>
              <CardTitle>Calculator Card Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <TouchableCard
                  onTap={() => console.log('Calculator card tapped')}
                  onLongPress={() => console.log('Calculator card long-pressed')}
                  onSwipeLeft={() => console.log('Calculator card swiped left')}
                  onSwipeRight={() => console.log('Calculator card swiped right')}
                  cardType="calculator"
                  enableHoverAnimations={true}
                  className="h-48"
                >
                  <div className="h-full flex flex-col">
                    <div className="flex items-start justify-between mb-4">
                      <div className="hover-icon text-3xl p-2 bg-blue-100 rounded-lg">
                        <Calculator className="w-8 h-8 text-blue-600" />
                      </div>
                      <Badge variant="secondary">Finance</Badge>
                    </div>
                    <h3 className="card-title font-semibold mb-2">Mortgage Calculator</h3>
                    <p className="text-sm text-muted-foreground mb-4">Calculate your monthly payments</p>
                    <div className="flex items-center justify-end mt-auto">
                      <ArrowRight className="hover-arrow w-4 h-4" />
                    </div>
                  </div>
                </TouchableCard>
              </div>
            </CardContent>
          </Card>

          {/* Button Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Button Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <TouchableButton
                  onTap={() => console.log('Primary button tapped')}
                  variant="primary"
                  size="lg"
                >
                  Primary Button
                </TouchableButton>
                
                <TouchableButton
                  onTap={() => console.log('Secondary button tapped')}
                  variant="secondary"
                  size="lg"
                >
                  Secondary Button
                </TouchableButton>
                
                <TouchableButton
                  onTap={() => console.log('Outline button tapped')}
                  variant="outline"
                  size="lg"
                >
                  Outline Button
                </TouchableButton>
                
                <TouchableButton
                  onTap={() => console.log('Ghost button tapped')}
                  variant="ghost"
                  size="lg"
                >
                  Ghost Button
                </TouchableButton>
              </div>
            </CardContent>
          </Card>

          {/* Calculator Button Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Calculator Button Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4 max-w-md mx-auto">
                <TouchableCalculatorButton
                  onClick={() => console.log('Number 7 pressed')}
                  variant="number"
                  size="lg"
                >
                  7
                </TouchableCalculatorButton>
                
                <TouchableCalculatorButton
                  onClick={() => console.log('Number 8 pressed')}
                  variant="number"
                  size="lg"
                >
                  8
                </TouchableCalculatorButton>
                
                <TouchableCalculatorButton
                  onClick={() => console.log('Number 9 pressed')}
                  variant="number"
                  size="lg"
                >
                  9
                </TouchableCalculatorButton>
                
                <TouchableCalculatorButton
                  onClick={() => console.log('Operator + pressed')}
                  variant="operator"
                  size="lg"
                >
                  +
                </TouchableCalculatorButton>
              </div>
            </CardContent>
          </Card>

          {/* Debug Information */}
          <Card>
            <CardHeader>
              <CardTitle>Debug Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h4 className="font-semibold mb-2">Expected CSS Classes:</h4>
                    <ul className="list-disc list-inside space-y-1">
                      <li><code>.touch-card.blog-card</code> - Blog cards</li>
                      <li><code>.touch-card.tool-card</code> - Tool cards</li>
                      <li><code>.touch-card.calculator-card</code> - Calculator cards</li>
                      <li><code>.hover-effect</code> - Hover capability</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Expected Hover Effects:</h4>
                    <ul className="list-disc list-inside space-y-1">
                      <li>Blog: <code>translateY(-8px) scale(1.02)</code></li>
                      <li>Tool: <code>translateY(-6px) scale(1.02)</code></li>
                      <li>Calculator: <code>translateY(-4px) scale(1.01)</code></li>
                      <li>Enhanced shadows and border colors</li>
                    </ul>
                  </div>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-semibold mb-2">CSS Media Query Test:</h4>
                  <p className="text-sm">
                    Open browser dev tools and check if <code>@media (hover: hover) and (pointer: fine)</code> is active.
                    This should be true on desktop with mouse, false on touch devices.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Testing Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Desktop (Mouse) Testing:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Hover over cards to see smooth animations</li>
                    <li>Cards should lift up and show shadow effects</li>
                    <li>Icons and arrows should animate on hover</li>
                    <li>Buttons should have subtle hover effects</li>
                    <li>Check browser dev tools for applied CSS classes</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Mobile/Tablet (Touch) Testing:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Tap cards for immediate feedback</li>
                    <li>Double-tap blog cards to like</li>
                    <li>Long press (750ms) for context menus</li>
                    <li>Swipe left/right on cards for actions</li>
                    <li>Check console for touch event logs</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
}
