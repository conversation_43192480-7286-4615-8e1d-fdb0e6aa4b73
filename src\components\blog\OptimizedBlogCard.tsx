'use client';

import React from "react";
import { motion } from "framer-motion";
import { Calendar, User, ArrowRight, Tag } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  slug: string;
  featuredImage?: string;
  image?: string;
  categoryName?: string;
  category?: string;
  publishedAt?: string;
  createdAt?: string;
  author: {
    name: string;
    email?: string;
  } | string;
  credit?: string;
}

interface OptimizedBlogCardProps {
  post: BlogPost;
  index?: number;
  showAnimation?: boolean;
  className?: string;
}

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.98
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94],
      delay: 0.1
    }
  }
};

const hoverVariants = {
  scale: 1.02,
  y: -8,
  transition: {
    duration: 0.3,
    ease: "easeOut"
  }
};

const imageVariants = {
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

export const OptimizedBlogCard = React.memo(function OptimizedBlogCard({
  post,
  index = 0,
  showAnimation = true,
  className = ""
}: OptimizedBlogCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Normalize post data
  const normalizedPost = {
    id: post.id || post._id,
    title: post.title,
    slug: post.slug,
    excerpt: post.excerpt || post.description || "",
    featuredImage: post.featuredImage || post.image || "",
    category: post.categoryName || post.category || "General",
    publishedAt: post.publishedAt || post.createdAt,
    author: typeof post.author === 'string' ? post.author : post.author?.name || "Admin",
    credit: post.credit || ""
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Recent';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'Technology': 'bg-blue-500/90 text-white',
      'Design': 'bg-purple-500/90 text-white',
      'Business': 'bg-green-500/90 text-white',
      'Marketing': 'bg-orange-500/90 text-white',
      'Development': 'bg-indigo-500/90 text-white',
      'AI': 'bg-pink-500/90 text-white',
      'Tools': 'bg-cyan-500/90 text-white',
      'General': 'bg-gray-500/90 text-white'
    };
    return colors[category as keyof typeof colors] || colors.General;
  };

  const defaultImage = 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80';

  return (
    <Link href={`/blog/${normalizedPost.slug}`} className="block group cursor-pointer">
      <motion.div
        variants={showAnimation ? cardVariants : undefined}
        initial={showAnimation ? "hidden" : false}
        animate={showAnimation ? "visible" : false}
        whileHover={hoverVariants}
        className={`relative bg-card border border-border rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 hover:border-primary/20 ${className}`}
        style={{ maxWidth: '350px', width: '100%', overflow: 'visible' }}
      >
        {/* Cover Image Section */}
        <div className="relative h-52 overflow-hidden bg-muted">
          {/* Loading State */}
          {imageLoading && (
            <div className="absolute inset-0 bg-muted animate-pulse flex items-center justify-center">
              <div className="w-12 h-12 rounded-full border-4 border-primary/20 border-t-primary animate-spin" />
            </div>
          )}
          
          {/* Image */}
          <motion.img
            src={!imageError && normalizedPost.featuredImage ? normalizedPost.featuredImage : defaultImage}
            alt={normalizedPost.title}
            className="w-full h-full object-cover transition-all duration-700"
            whileHover="hover"
            variants={imageVariants}
            onLoad={() => setImageLoading(false)}
            onError={() => {
              setImageError(true);
              setImageLoading(false);
            }}
            loading="lazy"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />

          {/* Category Badge */}
          <div className="absolute top-4 left-4">
            <Badge 
              className={`${getCategoryColor(normalizedPost.category)} border-0 shadow-lg backdrop-blur-sm`}
            >
              <Tag className="w-3 h-3 mr-1" />
              {normalizedPost.category}
            </Badge>
          </div>
        </div>

        {/* Content Section */}
        <div className="p-6 space-y-4">
          {/* Publication Date */}
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="w-4 h-4 mr-2" />
            {formatDate(normalizedPost.publishedAt)}
          </div>

          {/* Title */}
          <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-300 line-clamp-2">
            {normalizedPost.title}
          </h3>

          {/* Description */}
          {normalizedPost.excerpt && (
            <p className="text-muted-foreground text-sm line-clamp-3 leading-relaxed">
              {normalizedPost.excerpt}
            </p>
          )}

          {/* Author */}
          <div className="flex items-center text-sm text-muted-foreground">
            <User className="w-4 h-4 mr-2" />
            {normalizedPost.author}
          </div>

          {/* Read More Arrow */}
          <div className="flex items-center text-primary font-medium text-sm group-hover:translate-x-1 transition-transform duration-300">
            <span>Read more</span>
            <ArrowRight className="w-4 h-4 ml-2" />
          </div>
        </div>
      </motion.div>
    </Link>
  );
});
