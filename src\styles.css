/* ./src/styles.css */

/* Import touch-optimized styles */
@import './touch.css';

/* Import platform-specific styles */
@import './platform.css';

/* Define custom CSS variables */

/* Enhanced Blog Card Styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Enhanced animations for blog cards */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer {
  animation: shimmer 2s infinite;
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
.blog-card:focus-within {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Custom gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
:root, .light {
  /* Base colors - Light mode */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 248, 249, 250;
  --background-end-rgb: 255, 255, 255;
  --background: #f8f9fa;
  --foreground: #171717;

  /* Background colors in RGB format for CSS variables */
  --background-rgb: 248, 249, 250; /* #f8f9fa */

  /* UI Dashboard theme colors */
  --primary: 13, 110, 253;  /* #0d6efd - Blue */
  --primary-foreground: 255, 255, 255;
  --primary-light: 209, 227, 255;

  --secondary: 108, 117, 125; /* #6c757d - Gray */
  --secondary-foreground: 255, 255, 255;

  --accent: 25, 135, 84; /* #198754 - Green */
  --accent-foreground: 255, 255, 255;

  /* UI Elements */
  --card: 255, 255, 255;
  --card-foreground: 33, 37, 41;

  --muted: 233, 236, 239;
  --muted-foreground: 108, 117, 125;

  --border: 222, 226, 230;

  /* Status colors */
  --success: 25, 135, 84; /* #198754 - Green */
  --warning: 255, 193, 7; /* #ffc107 - Yellow */
  --error: 220, 53, 69; /* #dc3545 - Red */
  --info: 13, 202, 240; /* #0dcaf0 - Cyan */

  /* Chart colors */
  --chart-1: 13, 110, 253; /* Blue */
  --chart-2: 220, 53, 69; /* Red */
  --chart-3: 25, 135, 84; /* Green */
  --chart-4: 255, 193, 7; /* Yellow */
  --chart-5: 111, 66, 193; /* Purple */
}

/* Dark mode variables */
.dark {
  --background: #212529;
  --foreground: #f8f9fa;

  /* Background colors in RGB format for dark mode */
  --background-rgb: 33, 37, 41; /* #212529 */
  --background-start-rgb: 33, 37, 41;
  --background-end-rgb: 33, 37, 41;

  --card: 33, 37, 41; /* #212529 */
  --card-foreground: 248, 249, 250;

  --muted: 52, 58, 64; /* #343a40 */
  --muted-foreground: 173, 181, 189; /* #adb5bd */

  --border: 52, 58, 64; /* #343a40 */

  /* Ensure primary colors have good contrast in dark mode */
  --primary: 66, 135, 245; /* Brighter blue for dark mode */
  --primary-foreground: 255, 255, 255;
  --primary-light: 25, 91, 184;

  --secondary: 149, 97, 226; /* Brighter purple for dark mode */
  --secondary-foreground: 255, 255, 255;

  --accent: 56, 193, 114; /* Brighter green for dark mode */
  --accent-foreground: 255, 255, 255;

  /* Status colors with better visibility in dark mode */
  --success: 56, 193, 114; /* Brighter green */
  --warning: 255, 213, 79; /* Brighter yellow */
  --error: 241, 98, 111; /* Brighter red */
  --info: 66, 186, 255; /* Brighter cyan */

  /* Chart colors - brighter for dark mode */
  --chart-1: 66, 135, 245; /* Brighter blue */
  --chart-2: 241, 98, 111; /* Brighter red */
  --chart-3: 56, 193, 114; /* Brighter green */
  --chart-4: 255, 213, 79; /* Brighter yellow */
  --chart-5: 149, 97, 226; /* Brighter purple */
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Tool card hover effect */
.tool-card-hover-effect {
  transition: all 0.5s ease;
}

.tool-card-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.category-badge {
  transition: background-color 0.2s ease;
}

/* Animation for the arrow icon */
.arrow-transition {
  transition: transform 0.2s ease, color 0.2s ease;
}

/* Tool card animation */
.group:hover .tool-card {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Smooth transitions */
.tool-card {
  transition: all 0.5s ease;
}

@layer base {
  html {
    @apply transition-colors duration-300;
  }

  /* Font handling */
  :root {
    --font-fallback: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  /* Apply Inter font from local files */
  html {
    font-family: var(--font-inter), var(--font-fallback);
  }

  /* Fallback for when local fonts fail to load */
  html.font-fallback {
    font-family: var(--font-fallback);
  }
  @keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0%);
  }
}

.animate-slideIn {
  animation: slideIn 0.4s ease-out;
}


  body {
    @apply bg-[rgb(var(--background-rgb))] text-[var(--foreground)] transition-colors duration-300;
  }

  /* Improved heading styles */
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  /* YouTube-like progress bar styles */
  #nprogress {
    pointer-events: none;
  }

  #nprogress .bar {
    background: theme('colors.blue.600');
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
  }

  /* Fancy blur effect */
  #nprogress .peg {
    display: block;
    position: absolute;
    right: 0px;
    width: 100px;
    height: 100%;
    box-shadow: 0 0 10px theme('colors.blue.600'), 0 0 5px theme('colors.blue.600');
    opacity: 1.0;
    transform: rotate(3deg) translate(0px, -4px);
  }

  h1 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h2 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  h3 {
    @apply text-lg md:text-xl;
  }

  /* Improved form elements */
  input, select, textarea {
    @apply transition-colors duration-200;
  }
}

/* NProgress Styles */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: rgb(var(--primary));
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
}

#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px rgb(var(--primary)), 0 0 5px rgb(var(--primary));
  opacity: 1.0;
  transform: rotate(3deg) translate(0px, -4px);
}

/* TipTap Editor Styles */
.tiptap-editor {
  background-color: rgb(var(--background-rgb));
  color: var(--foreground);
  border-radius: 0.5rem;
  overflow: hidden;
}

.tiptap-editor .ProseMirror {
  min-height: 500px;
  max-height: none;
  overflow-y: auto;
  outline: none;
  font-size: 1.1rem;
  line-height: 1.7;
}

.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: rgb(var(--muted-foreground));
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Blog image styles */
.blog-image {
  display: inline-block;
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1rem 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.blog-image:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Image alignment */
.ProseMirror[data-text-align="left"] .blog-image {
  margin-right: auto;
  margin-left: 0;
}

.ProseMirror[data-text-align="center"] .blog-image {
  margin-left: auto;
  margin-right: auto;
}

/* Resizable image wrapper alignment */
.resizable-image-wrapper[data-alignment="left"] {
  text-align: left !important;
}

.resizable-image-wrapper[data-alignment="center"] {
  text-align: center !important;
}

.resizable-image-wrapper[data-alignment="right"] {
  text-align: right !important;
}

.resizable-image-wrapper .image-container {
  transition: all 0.2s ease;
}

/* Ensure proper image positioning */
.resizable-image-wrapper[data-alignment="left"] .image-container {
  text-align: left;
  margin-right: auto;
  margin-left: 0;
}

.resizable-image-wrapper[data-alignment="center"] .image-container {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}

.resizable-image-wrapper[data-alignment="right"] .image-container {
  text-align: right;
  margin-left: auto;
  margin-right: 0;
}

.ProseMirror[data-text-align="right"] .blog-image {
  margin-left: auto;
  margin-right: 0;
}

/* Image resizing handles */
.ProseMirror .image-resizer {
  display: inline-flex;
  position: relative;
}

.ProseMirror .image-resizer img {
  display: block;
  max-width: 100%;
  height: auto;
}

.ProseMirror .image-resizer__handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: rgb(var(--primary));
  border: 2px solid white;
  border-radius: 50%;
}

.ProseMirror .image-resizer__handle-tl {
  top: -6px;
  left: -6px;
  cursor: nwse-resize;
}

.ProseMirror .image-resizer__handle-tr {
  top: -6px;
  right: -6px;
  cursor: nesw-resize;
}

.ProseMirror .image-resizer__handle-bl {
  bottom: -6px;
  left: -6px;
  cursor: nesw-resize;
}

.ProseMirror .image-resizer__handle-br {
  bottom: -6px;
  right: -6px;
  cursor: nwse-resize;
}

/* Table styles */
.tiptap-editor table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  overflow: hidden;
}

.tiptap-editor table td,
.tiptap-editor table th {
  min-width: 1em;
  border: 2px solid rgb(var(--border));
  padding: 0.5rem;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.tiptap-editor table th {
  font-weight: bold;
  background-color: rgb(var(--muted));
}

/* Placeholder styles */
.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: rgb(var(--muted-foreground));
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Code block styles */
.tiptap-editor pre {
  background-color: rgb(var(--muted));
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-family: monospace;
  overflow-x: auto;
}

/* Blockquote styles */
.tiptap-editor blockquote {
  border-left: 4px solid rgb(var(--primary));
  padding-left: 1rem;
  font-style: italic;
}

/* Horizontal rule styles */
.tiptap-editor hr {
  border: none;
  border-top: 2px solid rgb(var(--border));
  margin: 2rem 0;
}

/* Custom utility classes */
@layer utilities {
  /* Text colors with proper contrast */
  .text-adaptive {
    @apply text-[var(--foreground)] transition-colors duration-200;
  }

  .text-adaptive-muted {
    @apply text-[var(--muted-foreground)] transition-colors duration-200;
  }

  /* Ensure text is always readable regardless of background */
  .text-on-primary {
    @apply text-[rgb(var(--primary-foreground))];
  }

  .text-on-secondary {
    @apply text-[rgb(var(--secondary-foreground))];
  }

  .text-on-accent {
    @apply text-[rgb(var(--accent-foreground))];
  }

  /* Card styles with proper dark mode support */
  .card-adaptive {
    @apply bg-[rgb(var(--card))] text-[rgb(var(--card-foreground))] border border-[rgb(var(--border))] rounded-lg shadow-sm;
  }

  /* Button styles with proper hover states */
  .btn-primary {
    @apply bg-[rgb(var(--primary))] text-[rgb(var(--primary-foreground))] hover:bg-[rgb(var(--primary))/90] transition-colors;
  }

  .btn-secondary {
    @apply bg-[rgb(var(--secondary))] text-[rgb(var(--secondary-foreground))] hover:bg-[rgb(var(--secondary))/90] transition-colors;
  }

  .btn-accent {
    @apply bg-[rgb(var(--accent))] text-[rgb(var(--accent-foreground))] hover:bg-[rgb(var(--accent))/90] transition-colors;
  }

  .btn-outline {
    @apply border border-[rgb(var(--border))] bg-transparent hover:bg-[rgb(var(--muted))/50] transition-colors;
  }

  /* Status indicators */
  .status-success {
    @apply text-[rgb(var(--success))];
  }

  .status-warning {
    @apply text-[rgb(var(--warning))];
  }

  .status-error {
    @apply text-[rgb(var(--error))];
  }

  .status-info {
    @apply text-[rgb(var(--info))];
  }

  /* Responsive utilities */
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .responsive-grid-dashboard {
    @apply grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6;
  }

  /* Glass effect utilities */
  .glass-effect {
    @apply bg-[rgb(var(--card))]/80 backdrop-blur-md border border-[rgb(var(--border))];
  }

  .glass-effect-dark {
    @apply dark:bg-[rgb(var(--card))]/60 dark:backdrop-blur-md dark:border-[rgb(var(--border))];
  }

  /* Chart and data visualization */
  .chart-container {
    @apply rounded-lg p-4 bg-[rgb(var(--card))] border border-[rgb(var(--border))] h-[300px];
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-[rgb(var(--primary))] to-[rgb(var(--accent))];
  }

  /* Improved focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-[rgb(var(--primary))]/50 focus:ring-offset-2 focus:ring-offset-[rgb(var(--background-rgb))];
  }

  /* Background utilities */
  .bg-adaptive {
    @apply bg-[rgb(var(--background-rgb))] transition-colors duration-300;
  }

  .bg-adaptive-card {
    @apply bg-[rgb(var(--card))] transition-colors duration-300;
  }

  .bg-adaptive-muted {
    @apply bg-[rgb(var(--muted))] transition-colors duration-300;
  }
}
