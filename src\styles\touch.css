/* Touch-optimized styles for mobile and tablet devices */

/* Touch device detection and base styles */
@media (hover: none) and (pointer: coarse) {
  /* Disable hover effects on touch devices */
  .hover-effect:hover,
  .touch-card:hover,
  .touch-button:hover,
  .calculator-button:hover {
    transform: none !important;
    box-shadow: none !important;
    background: inherit !important;
  }

  /* Ensure touch interactions work on touch devices */
  .touch-card:active {
    transform: scale(0.98) !important;
  }
}

/* Desktop hover animations - only active on devices with hover capability */
@media (hover: hover) and (pointer: fine) {
  /* Disable touch active states on hover devices */
  .touch-card:active {
    transform: none !important;
  }

  /* Disable framer-motion transforms that might conflict with CSS hover */
  .touch-card[style*="transform"] {
    transform: none !important;
  }

  /* Debug: Add visual indicator for hover-capable devices */
  .hover-effect::before {
    content: '🖱️';
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 12px;
    opacity: 0.5;
    z-index: 10;
    pointer-events: none;
  }

  /* Blog card hover animations */
  .touch-card.blog-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  .touch-card.blog-card:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    border-color: #3b82f6 !important;
  }

  /* Force hover state to override any inline styles */
  .touch-card.blog-card:hover[style] {
    transform: translateY(-8px) scale(1.02) !important;
  }

  .touch-card.blog-card:hover .blog-image {
    transform: scale(1.05) !important;
  }

  /* Tool card hover animations */
  .touch-card.tool-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  .touch-card.tool-card:hover {
    transform: translateY(-6px) scale(1.02) !important;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12) !important;
    border-color: #3b82f6 !important;
  }

  /* Force hover state to override any inline styles */
  .touch-card.tool-card:hover[style] {
    transform: translateY(-6px) scale(1.02) !important;
  }

  .touch-card.tool-card:hover .tool-icon {
    transform: scale(1.1) rotate(5deg) !important;
  }

  /* Calculator card hover animations */
  .touch-card.calculator-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .touch-card.calculator-card:hover {
    transform: translateY(-4px) scale(1.01) !important;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1) !important;
  }

  /* Button hover animations */
  .touch-button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .touch-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  }

  /* Calculator button hover animations */
  .calculator-button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .calculator-button:hover {
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .calculator-button.operator:hover {
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3) !important;
  }

  .calculator-button.equals:hover {
    box-shadow: 0 6px 12px rgba(34, 197, 94, 0.3) !important;
  }

  /* Navigation hover animations */
  .touch-nav-item {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .touch-nav-item:hover {
    background-color: var(--accent);
    transform: translateX(4px);
  }

  /* Arrow and icon hover animations */
  .hover-arrow {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .touch-card:hover .hover-arrow {
    transform: translateX(4px);
  }

  .hover-icon {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .touch-card:hover .hover-icon {
    transform: scale(1.1);
  }

  /* Smooth focus transitions for keyboard navigation */
  .touch-button:focus-visible,
  .touch-card:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    transition: outline-offset 0.2s ease;
  }

  /* Enhanced hover states for interactive elements */
  .touch-card:hover .category-badge {
    transform: scale(1.05);
  }

  .touch-card:hover .card-title {
    color: var(--primary);
  }

  /* Staggered hover animations for card content */
  .touch-card:hover .card-content > * {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
  }

  .touch-card:hover .card-content > *:nth-child(2) {
    transition-delay: 0.05s;
  }

  .touch-card:hover .card-content > *:nth-child(3) {
    transition-delay: 0.1s;
  }

  /* Hover glow effects */
  .touch-card:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(var(--primary-rgb), 0.1), transparent);
    opacity: 0;
    animation: shimmer 2s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
  }
}

/* Touch target minimum sizes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.touch-target-large {
  min-height: 56px;
  min-width: 56px;
}

/* Touch feedback states */
.touch-active {
  transform: scale(0.95);
  transition: transform 0.1s ease-out;
}

.touch-pressed {
  transform: scale(0.98);
  opacity: 0.8;
  transition: all 0.1s ease-out;
}

.touch-released {
  transform: scale(1.02);
  transition: all 0.2s ease-out;
}

/* Touch-friendly button styles */
.touch-button {
  @apply touch-target;
  border-radius: 12px;
  transition: all 0.2s ease-out;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.touch-button:active {
  @apply touch-active;
}

/* Card touch interactions */
.touch-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

/* Touch active state moved to touch device media query to prevent hover conflicts */

.touch-card.touch-long-press {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Swipe indicators */
.swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s ease-out;
  pointer-events: none;
}

.swipe-indicator.left {
  left: 10px;
}

.swipe-indicator.right {
  right: 10px;
}

.swipe-indicator.visible {
  opacity: 1;
}

/* Pull to refresh styles */
.pull-to-refresh {
  position: relative;
  overflow: hidden;
}

.pull-indicator {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease-out;
}

.pull-indicator.active {
  top: 20px;
  background: rgba(59, 130, 246, 0.2);
}

/* Touch-optimized navigation */
.touch-nav {
  padding: 8px 16px;
  min-height: 56px;
}

.touch-nav-item {
  @apply touch-target;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.2s ease-out;
}

.touch-nav-item:active {
  background-color: rgba(59, 130, 246, 0.1);
  transform: scale(0.98);
}

/* Mobile menu styles */
.mobile-menu-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 40;
}

.mobile-menu {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  max-width: 320px;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 50;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.dark .mobile-menu {
  background: #1f2937;
}

/* Touch-optimized form elements */
.touch-input {
  min-height: 48px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 16px; /* Prevents zoom on iOS */
}

.touch-select {
  min-height: 48px;
  padding: 12px 16px;
  border-radius: 8px;
}

/* Calculator button styles */
.calculator-button {
  @apply touch-target-large;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.15s ease-out;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.calculator-button:active {
  transform: scale(0.95);
}

.calculator-button.operator {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.calculator-button.number {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  color: #374151;
}

.dark .calculator-button.number {
  background: linear-gradient(135deg, #374151, #1f2937);
  color: #f3f4f6;
}

/* Touch-optimized scrolling */
.touch-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Disable text selection on touch elements */
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* iOS specific fixes */
@supports (-webkit-touch-callout: none) {
  .ios-fix {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
  }
  
  /* Fix for iOS keyboard resize */
  .ios-keyboard-fix {
    height: 100vh;
    height: -webkit-fill-available;
  }
}

/* Android specific fixes */
@media screen and (max-width: 768px) {
  .android-fix {
    -webkit-tap-highlight-color: transparent;
  }
}

/* Touch-friendly spacing */
.touch-spacing {
  padding: 16px;
  gap: 16px;
}

.touch-spacing-sm {
  padding: 12px;
  gap: 12px;
}

.touch-spacing-lg {
  padding: 24px;
  gap: 24px;
}

/* Gesture hint animations */
@keyframes swipe-hint {
  0% { transform: translateX(0); opacity: 1; }
  50% { transform: translateX(20px); opacity: 0.7; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes long-press-hint {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.swipe-hint {
  animation: swipe-hint 2s ease-in-out infinite;
}

.long-press-hint {
  animation: long-press-hint 1.5s ease-in-out infinite;
}

/* Touch-optimized grid layouts */
.touch-grid {
  display: grid;
  gap: 16px;
  padding: 16px;
}

.touch-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.touch-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 640px) {
  .touch-grid-responsive {
    grid-template-columns: 1fr;
  }
}

/* Touch feedback for different interaction types */
.touch-feedback-tap {
  transition: transform 0.1s ease-out;
}

.touch-feedback-tap:active {
  transform: scale(0.98);
}

.touch-feedback-swipe {
  transition: transform 0.2s ease-out;
}

.touch-feedback-long-press {
  transition: all 0.3s ease-out;
}

/* Accessibility improvements for touch */
.touch-accessible {
  outline: none;
  border: 2px solid transparent;
  transition: border-color 0.2s ease-out;
}

.touch-accessible:focus-visible {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .touch-button,
  .touch-card {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .touch-card,
  .touch-button,
  .calculator-button {
    transition: none;
  }
  
  .swipe-hint,
  .long-press-hint {
    animation: none;
  }
}

/* Fallback hover animations for testing - these should work on all devices */
.test-hover-card {
  transition: all 0.3s ease !important;
  border: 2px solid #e5e7eb !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
}

.test-hover-card:hover {
  transform: translateY(-8px) scale(1.02) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  border-color: #3b82f6 !important;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
}

/* Debug styles to verify CSS is loading */
.debug-css-loaded {
  position: relative;
}

.debug-css-loaded::after {
  content: '✅ CSS Loaded';
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 10px;
  color: #10b981;
  background: #ecfdf5;
  padding: 2px 6px;
  border-radius: 4px;
  z-index: 1000;
}
